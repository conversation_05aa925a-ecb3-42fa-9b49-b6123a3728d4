// Snake Game JavaScript
class SnakeGame {
    constructor() {
        this.canvas = document.getElementById('gameCanvas');
        this.ctx = this.canvas.getContext('2d');
        this.gridSize = 20;
        this.tileCount = {
            x: this.canvas.width / this.gridSize,
            y: this.canvas.height / this.gridSize
        };
        
        this.snake = [{ x: 10, y: 10 }];
        this.food = this.generateFood();
        this.dx = 0;
        this.dy = 0;
        this.score = 0;
        this.gameRunning = false;
        this.gameLoop = null;
        
        // Colors for rainbow snake
        this.snakeColors = [
            '#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', 
            '#feca57', '#ff9ff3', '#54a0ff', '#5f27cd',
            '#00d2d3', '#ff9f43', '#10ac84', '#ee5a24'
        ];
        
        // Food colors
        this.foodColors = [
            '#feca57', '#ff9ff3', '#54a0ff', '#ff6b6b',
            '#4ecdc4', '#96ceb4', '#5f27cd', '#10ac84'
        ];
        
        this.currentFoodColor = 0;
        this.animationFrame = 0;
        
        this.initializeGame();
        this.setupEventListeners();
        this.loadHighScore();
    }
    
    initializeGame() {
        this.drawGame();
        this.updateUI();
    }
    
    setupEventListeners() {
        document.addEventListener('keydown', (e) => {
            if (!this.gameRunning && (e.key === ' ' || e.key === 'Enter')) {
                this.startGame();
                return;
            }
            
            if (this.gameRunning) {
                this.handleKeyPress(e);
            }
        });
    }
    
    handleKeyPress(e) {
        const key = e.key.toLowerCase();
        
        // Prevent reverse direction
        if ((key === 'arrowleft' || key === 'a') && this.dx !== 1) {
            this.dx = -1;
            this.dy = 0;
        } else if ((key === 'arrowright' || key === 'd') && this.dx !== -1) {
            this.dx = 1;
            this.dy = 0;
        } else if ((key === 'arrowup' || key === 'w') && this.dy !== 1) {
            this.dx = 0;
            this.dy = -1;
        } else if ((key === 'arrowdown' || key === 's') && this.dy !== -1) {
            this.dx = 0;
            this.dy = 1;
        }
    }
    
    startGame() {
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
        }
        
        this.gameRunning = true;
        this.gameLoop = setInterval(() => {
            this.update();
            this.drawGame();
        }, 150); // Game speed
    }
    
    update() {
        if (!this.gameRunning) return;
        
        this.animationFrame++;
        
        // Move snake
        const head = { x: this.snake[0].x + this.dx, y: this.snake[0].y + this.dy };
        
        // Check wall collision
        if (head.x < 0 || head.x >= this.tileCount.x || 
            head.y < 0 || head.y >= this.tileCount.y) {
            this.gameOver();
            return;
        }
        
        // Check self collision
        for (let segment of this.snake) {
            if (head.x === segment.x && head.y === segment.y) {
                this.gameOver();
                return;
            }
        }
        
        this.snake.unshift(head);
        
        // Check food collision
        if (head.x === this.food.x && head.y === this.food.y) {
            this.score += 10;
            this.food = this.generateFood();
            this.currentFoodColor = (this.currentFoodColor + 1) % this.foodColors.length;
            this.createFoodParticles(head.x * this.gridSize, head.y * this.gridSize);
        } else {
            this.snake.pop();
        }
        
        this.updateUI();
    }
    
    generateFood() {
        let food;
        do {
            food = {
                x: Math.floor(Math.random() * this.tileCount.x),
                y: Math.floor(Math.random() * this.tileCount.y)
            };
        } while (this.snake.some(segment => segment.x === food.x && segment.y === food.y));
        
        return food;
    }
    
    drawGame() {
        // Clear canvas with gradient background
        const gradient = this.ctx.createLinearGradient(0, 0, this.canvas.width, this.canvas.height);
        gradient.addColorStop(0, 'rgba(102, 126, 234, 0.1)');
        gradient.addColorStop(0.5, 'rgba(240, 147, 251, 0.1)');
        gradient.addColorStop(1, 'rgba(67, 233, 123, 0.1)');
        
        this.ctx.fillStyle = gradient;
        this.ctx.fillRect(0, 0, this.canvas.width, this.canvas.height);
        
        // Draw grid
        this.drawGrid();
        
        // Draw snake
        this.drawSnake();
        
        // Draw food
        this.drawFood();
    }
    
    drawGrid() {
        this.ctx.strokeStyle = 'rgba(255, 255, 255, 0.1)';
        this.ctx.lineWidth = 1;
        
        for (let x = 0; x <= this.canvas.width; x += this.gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(x, 0);
            this.ctx.lineTo(x, this.canvas.height);
            this.ctx.stroke();
        }
        
        for (let y = 0; y <= this.canvas.height; y += this.gridSize) {
            this.ctx.beginPath();
            this.ctx.moveTo(0, y);
            this.ctx.lineTo(this.canvas.width, y);
            this.ctx.stroke();
        }
    }
    
    drawSnake() {
        this.snake.forEach((segment, index) => {
            const x = segment.x * this.gridSize;
            const y = segment.y * this.gridSize;
            
            // Create gradient for each segment
            const gradient = this.ctx.createRadialGradient(
                x + this.gridSize/2, y + this.gridSize/2, 0,
                x + this.gridSize/2, y + this.gridSize/2, this.gridSize/2
            );
            
            const colorIndex = index % this.snakeColors.length;
            gradient.addColorStop(0, this.snakeColors[colorIndex]);
            gradient.addColorStop(1, this.adjustBrightness(this.snakeColors[colorIndex], -30));
            
            this.ctx.fillStyle = gradient;
            
            // Draw rounded rectangle for snake segment
            this.drawRoundedRect(x + 1, y + 1, this.gridSize - 2, this.gridSize - 2, 8);
            
            // Add shine effect for head
            if (index === 0) {
                const shineGradient = this.ctx.createLinearGradient(x, y, x + this.gridSize, y + this.gridSize);
                shineGradient.addColorStop(0, 'rgba(255, 255, 255, 0.4)');
                shineGradient.addColorStop(1, 'rgba(255, 255, 255, 0)');
                this.ctx.fillStyle = shineGradient;
                this.drawRoundedRect(x + 1, y + 1, this.gridSize - 2, this.gridSize - 2, 8);
                
                // Draw eyes
                this.drawEyes(x, y);
            }
        });
    }
    
    drawEyes(x, y) {
        const eyeSize = 3;
        const eyeOffset = 6;
        
        this.ctx.fillStyle = 'white';
        this.ctx.beginPath();
        this.ctx.arc(x + eyeOffset, y + eyeOffset, eyeSize, 0, 2 * Math.PI);
        this.ctx.fill();
        
        this.ctx.beginPath();
        this.ctx.arc(x + this.gridSize - eyeOffset, y + eyeOffset, eyeSize, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // Pupils
        this.ctx.fillStyle = 'black';
        this.ctx.beginPath();
        this.ctx.arc(x + eyeOffset, y + eyeOffset, 1, 0, 2 * Math.PI);
        this.ctx.fill();
        
        this.ctx.beginPath();
        this.ctx.arc(x + this.gridSize - eyeOffset, y + eyeOffset, 1, 0, 2 * Math.PI);
        this.ctx.fill();
    }
    
    drawFood() {
        const x = this.food.x * this.gridSize;
        const y = this.food.y * this.gridSize;
        
        // Animated pulsing effect
        const pulseScale = 1 + Math.sin(this.animationFrame * 0.2) * 0.1;
        const size = (this.gridSize - 4) * pulseScale;
        const offset = (this.gridSize - size) / 2;
        
        // Create radial gradient for food
        const gradient = this.ctx.createRadialGradient(
            x + this.gridSize/2, y + this.gridSize/2, 0,
            x + this.gridSize/2, y + this.gridSize/2, size/2
        );
        
        const currentColor = this.foodColors[this.currentFoodColor];
        gradient.addColorStop(0, currentColor);
        gradient.addColorStop(0.7, this.adjustBrightness(currentColor, -20));
        gradient.addColorStop(1, this.adjustBrightness(currentColor, -40));
        
        this.ctx.fillStyle = gradient;
        this.ctx.beginPath();
        this.ctx.arc(x + this.gridSize/2, y + this.gridSize/2, size/2, 0, 2 * Math.PI);
        this.ctx.fill();
        
        // Add sparkle effect
        this.drawSparkles(x + this.gridSize/2, y + this.gridSize/2);
    }
    
    drawSparkles(centerX, centerY) {
        const sparkleCount = 4;
        const sparkleRadius = 15;
        
        for (let i = 0; i < sparkleCount; i++) {
            const angle = (this.animationFrame * 0.1 + i * Math.PI / 2) % (2 * Math.PI);
            const x = centerX + Math.cos(angle) * sparkleRadius;
            const y = centerY + Math.sin(angle) * sparkleRadius;
            
            this.ctx.fillStyle = 'rgba(255, 255, 255, 0.8)';
            this.ctx.beginPath();
            this.ctx.arc(x, y, 2, 0, 2 * Math.PI);
            this.ctx.fill();
        }
    }
    
    createFoodParticles(x, y) {
        // Simple particle effect when food is eaten
        for (let i = 0; i < 8; i++) {
            setTimeout(() => {
                const particle = document.createElement('div');
                particle.style.position = 'absolute';
                particle.style.left = x + 'px';
                particle.style.top = y + 'px';
                particle.style.width = '4px';
                particle.style.height = '4px';
                particle.style.backgroundColor = this.foodColors[this.currentFoodColor];
                particle.style.borderRadius = '50%';
                particle.style.pointerEvents = 'none';
                particle.style.zIndex = '1000';
                
                document.body.appendChild(particle);
                
                const angle = (i / 8) * 2 * Math.PI;
                const distance = 30;
                const endX = x + Math.cos(angle) * distance;
                const endY = y + Math.sin(angle) * distance;
                
                particle.animate([
                    { transform: `translate(0, 0)`, opacity: 1 },
                    { transform: `translate(${endX - x}px, ${endY - y}px)`, opacity: 0 }
                ], {
                    duration: 500,
                    easing: 'ease-out'
                }).onfinish = () => {
                    document.body.removeChild(particle);
                };
            }, i * 50);
        }
    }
    
    drawRoundedRect(x, y, width, height, radius) {
        this.ctx.beginPath();
        this.ctx.moveTo(x + radius, y);
        this.ctx.lineTo(x + width - radius, y);
        this.ctx.quadraticCurveTo(x + width, y, x + width, y + radius);
        this.ctx.lineTo(x + width, y + height - radius);
        this.ctx.quadraticCurveTo(x + width, y + height, x + width - radius, y + height);
        this.ctx.lineTo(x + radius, y + height);
        this.ctx.quadraticCurveTo(x, y + height, x, y + height - radius);
        this.ctx.lineTo(x, y + radius);
        this.ctx.quadraticCurveTo(x, y, x + radius, y);
        this.ctx.closePath();
        this.ctx.fill();
    }
    
    adjustBrightness(color, amount) {
        const num = parseInt(color.replace("#", ""), 16);
        const amt = Math.round(2.55 * amount);
        const R = (num >> 16) + amt;
        const G = (num >> 8 & 0x00FF) + amt;
        const B = (num & 0x0000FF) + amt;
        return "#" + (0x1000000 + (R < 255 ? R < 1 ? 0 : R : 255) * 0x10000 +
            (G < 255 ? G < 1 ? 0 : G : 255) * 0x100 +
            (B < 255 ? B < 1 ? 0 : B : 255)).toString(16).slice(1);
    }
    
    updateUI() {
        document.getElementById('score').textContent = this.score;
        document.getElementById('length').textContent = this.snake.length;
    }
    
    gameOver() {
        this.gameRunning = false;
        clearInterval(this.gameLoop);
        
        // Update high score
        const highScore = parseInt(localStorage.getItem('snakeHighScore') || '0');
        if (this.score > highScore) {
            localStorage.setItem('snakeHighScore', this.score.toString());
            document.getElementById('highScore').textContent = this.score;
        }
        
        // Show game over screen
        document.getElementById('finalScore').textContent = this.score;
        document.getElementById('finalLength').textContent = this.snake.length;
        document.getElementById('gameOver').style.display = 'block';
    }
    
    loadHighScore() {
        const highScore = localStorage.getItem('snakeHighScore') || '0';
        document.getElementById('highScore').textContent = highScore;
    }
    
    restart() {
        this.snake = [{ x: 10, y: 10 }];
        this.food = this.generateFood();
        this.dx = 0;
        this.dy = 0;
        this.score = 0;
        this.gameRunning = false;
        this.currentFoodColor = 0;
        this.animationFrame = 0;
        
        document.getElementById('gameOver').style.display = 'none';
        
        if (this.gameLoop) {
            clearInterval(this.gameLoop);
        }
        
        this.drawGame();
        this.updateUI();
    }
}

// Initialize game
let game;

window.addEventListener('load', () => {
    game = new SnakeGame();
});

function restartGame() {
    game.restart();
}

// Auto-start game after 2 seconds
setTimeout(() => {
    if (game && !game.gameRunning) {
        game.startGame();
    }
}, 2000);
