// Enhanced Bird Blast Game with Advanced Systems
const canvas = document.getElementById('gameCanvas');
const ctx = canvas.getContext('2d');

// Performance monitoring
let performanceStats = {
    fps: 0,
    frameCount: 0,
    lastTime: 0,
    deltaTime: 0
};

// Game state
let gameState = {
    score: 0,
    level: 1,
    birds: 3,
    isAiming: false,
    isDragging: false,
    gameOver: false,
    paused: false,
    powerUsed: false,
    combo: 0,
    maxCombo: 0,
    timeScale: 1
};

// Initialize systems
const physics = new PhysicsEngine();
const particleSystem = new ParticleSystem(canvas, ctx);

// Performance optimization - object pooling
const objectPools = {
    birds: [],
    targets: [],
    effects: []
};

// Game objects
let slingshot = { x: 100, y: 450, radius: 35 };
let bird = null;
let trajectory = [];
let targets = [];
let environment = {
    wind: { x: 0, y: 0 },
    gravity: 0.4
};

// Enhanced color palette
const colors = {
    bird: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57'],
    slingshot: '#8B4513',
    target: ['#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4', '#FECA57', '#DDA0DD'],
    ground: '#90EE90',
    sky: '#87CEEB',
    trail: '#FFD93D'
};

// Bird types with different abilities
const birdTypes = {
    normal: { color: '#FF6B6B', ability: null, mass: 1 },
    heavy: { color: '#8B4513', ability: 'heavy', mass: 2 },
    speed: { color: '#4ECDC4', ability: 'speed', mass: 0.7 },
    explosive: { color: '#FF4757', ability: 'explosive', mass: 1.2 }
};

// Initialize game
function init() {
    // Setup canvas for high DPI displays
    setupCanvas();

    // Create initial targets
    createTargets();

    // Start game loop
    gameLoop();

    console.log('🎮 Enhanced Bird Blast initialized!');
}

// Setup canvas for crisp rendering
function setupCanvas() {
    const dpr = window.devicePixelRatio || 1;
    const rect = canvas.getBoundingClientRect();

    canvas.width = rect.width * dpr;
    canvas.height = rect.height * dpr;

    ctx.scale(dpr, dpr);
    canvas.style.width = rect.width + 'px';
    canvas.style.height = rect.height + 'px';
}

// Create targets with physics bodies
function createTargets() {
    // Clear existing targets from physics
    targets.forEach(target => {
        if (target.physicsBody) {
            physics.removeBody(target.physicsBody);
        }
    });

    targets = [];
    const numTargets = Math.min(3 + Math.floor(gameState.level * 1.5), 12);
    const targetColors = colors.target;

    for (let i = 0; i < numTargets; i++) {
        const x = 500 + i * 70 + Math.random() * 50;
        const y = 350 + Math.random() * 150 + Math.sin(i) * 50;
        const radius = 15 + Math.random() * 10;
        const targetType = Math.random() < 0.3 ? 'special' : 'normal';

        const target = {
            x: x,
            y: y,
            radius: radius,
            color: targetColors[i % targetColors.length],
            destroyed: false,
            health: targetType === 'special' ? 2 : 1,
            maxHealth: targetType === 'special' ? 2 : 1,
            type: targetType,
            points: targetType === 'special' ? 200 : 100,
            lastHit: 0
        };

        // Create physics body
        target.physicsBody = physics.addBody(new PhysicsBody(x, y, {
            radius: radius,
            isStatic: true,
            shape: 'circle',
            color: target.color,
            onCollision: (other, collision) => {
                if (other === bird?.physicsBody) {
                    hitTarget(target, collision);
                }
            }
        }));

        targets.push(target);
    }
}

// Bird class
class Bird {
    constructor(x, y) {
        this.x = x;
        this.y = y;
        this.vx = 0;
        this.vy = 0;
        this.radius = 15;
        this.launched = false;
        this.trail = [];
    }

    update() {
        if (this.launched) {
            this.vy += GRAVITY;
            this.vx *= FRICTION;
            this.vy *= FRICTION;
            
            this.x += this.vx;
            this.y += this.vy;

            // Add to trail
            this.trail.push({ x: this.x, y: this.y });
            if (this.trail.length > 20) {
                this.trail.shift();
            }

            // Bounce off walls
            if (this.x - this.radius < 0 || this.x + this.radius > canvas.width) {
                this.vx *= -BOUNCE;
                this.x = Math.max(this.radius, Math.min(canvas.width - this.radius, this.x));
            }

            // Ground collision
            if (this.y + this.radius > canvas.height - 50) {
                this.y = canvas.height - 50 - this.radius;
                this.vy *= -BOUNCE;
                this.vx *= FRICTION;
            }

            // Check target collisions
            this.checkTargetCollisions();
        }
    }

    checkTargetCollisions() {
        targets.forEach(target => {
            if (!target.destroyed) {
                const dx = this.x - target.x;
                const dy = this.y - target.y;
                const distance = Math.sqrt(dx * dx + dy * dy);

                if (distance < this.radius + target.radius) {
                    target.destroyed = true;
                    gameState.score += 100;
                    createExplosion(target.x, target.y, target.color);
                    
                    // Bounce bird
                    this.vx *= -0.5;
                    this.vy *= -0.5;
                }
            }
        });
    }

    draw() {
        // Draw trail
        ctx.strokeStyle = 'rgba(255, 107, 107, 0.5)';
        ctx.lineWidth = 3;
        ctx.beginPath();
        for (let i = 0; i < this.trail.length - 1; i++) {
            const alpha = i / this.trail.length;
            ctx.globalAlpha = alpha;
            if (i === 0) {
                ctx.moveTo(this.trail[i].x, this.trail[i].y);
            } else {
                ctx.lineTo(this.trail[i].x, this.trail[i].y);
            }
        }
        ctx.stroke();
        ctx.globalAlpha = 1;

        // Draw bird
        ctx.fillStyle = colors.bird;
        ctx.beginPath();
        ctx.arc(this.x, this.y, this.radius, 0, Math.PI * 2);
        ctx.fill();
        
        // Bird face
        ctx.fillStyle = 'white';
        ctx.beginPath();
        ctx.arc(this.x - 5, this.y - 3, 4, 0, Math.PI * 2);
        ctx.fill();
        ctx.fillStyle = 'black';
        ctx.beginPath();
        ctx.arc(this.x - 5, this.y - 3, 2, 0, Math.PI * 2);
        ctx.fill();
        
        // Beak
        ctx.fillStyle = 'orange';
        ctx.beginPath();
        ctx.moveTo(this.x + this.radius - 5, this.y);
        ctx.lineTo(this.x + this.radius + 5, this.y - 2);
        ctx.lineTo(this.x + this.radius + 5, this.y + 2);
        ctx.fill();
    }
}

// Create explosion particles
function createExplosion(x, y, color) {
    for (let i = 0; i < 15; i++) {
        particles.push({
            x: x,
            y: y,
            vx: (Math.random() - 0.5) * 10,
            vy: (Math.random() - 0.5) * 10,
            life: 30,
            maxLife: 30,
            color: color
        });
    }
}

// Update particles
function updateParticles() {
    for (let i = particles.length - 1; i >= 0; i--) {
        const p = particles[i];
        p.x += p.vx;
        p.y += p.vy;
        p.vy += 0.2;
        p.life--;
        
        if (p.life <= 0) {
            particles.splice(i, 1);
        }
    }
}

// Draw everything
function draw() {
    // Clear canvas with gradient background
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, colors.sky);
    gradient.addColorStop(1, colors.ground);
    ctx.fillStyle = gradient;
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // Draw ground
    ctx.fillStyle = colors.ground;
    ctx.fillRect(0, canvas.height - 50, canvas.width, 50);

    // Draw slingshot
    ctx.fillStyle = colors.slingshot;
    ctx.fillRect(slingshot.x - 5, slingshot.y - 50, 10, 50);
    ctx.beginPath();
    ctx.arc(slingshot.x, slingshot.y, slingshot.radius, 0, Math.PI * 2);
    ctx.fill();

    // Draw slingshot band when aiming
    if (gameState.isAiming && bird && !bird.launched) {
        ctx.strokeStyle = '#8B4513';
        ctx.lineWidth = 4;
        ctx.beginPath();
        ctx.moveTo(slingshot.x - 15, slingshot.y - 20);
        ctx.lineTo(bird.x, bird.y);
        ctx.lineTo(slingshot.x + 15, slingshot.y - 20);
        ctx.stroke();
    }

    // Draw trajectory line when aiming
    if (gameState.isAiming && bird && !bird.launched) {
        ctx.strokeStyle = 'rgba(255, 255, 255, 0.8)';
        ctx.lineWidth = 3;
        ctx.setLineDash([8, 4]);
        ctx.beginPath();

        for (let i = 0; i < trajectory.length - 1; i++) {
            const alpha = 1 - (i / trajectory.length);
            ctx.globalAlpha = alpha * 0.8;

            if (i === 0) {
                ctx.moveTo(trajectory[i].x, trajectory[i].y);
            } else {
                ctx.lineTo(trajectory[i].x, trajectory[i].y);
            }
        }
        ctx.stroke();
        ctx.setLineDash([]);
        ctx.globalAlpha = 1;

        // Draw trajectory dots
        ctx.fillStyle = 'rgba(255, 255, 255, 0.9)';
        for (let i = 0; i < trajectory.length; i += 3) {
            const alpha = 1 - (i / trajectory.length);
            ctx.globalAlpha = alpha;
            ctx.beginPath();
            ctx.arc(trajectory[i].x, trajectory[i].y, 2, 0, Math.PI * 2);
            ctx.fill();
        }
        ctx.globalAlpha = 1;
    }

    // Draw targets
    targets.forEach(target => {
        if (!target.destroyed) {
            ctx.fillStyle = target.color;
            ctx.beginPath();
            ctx.arc(target.x, target.y, target.radius, 0, Math.PI * 2);
            ctx.fill();
            
            // Target face
            ctx.fillStyle = 'white';
            ctx.beginPath();
            ctx.arc(target.x - 5, target.y - 5, 3, 0, Math.PI * 2);
            ctx.arc(target.x + 5, target.y - 5, 3, 0, Math.PI * 2);
            ctx.fill();
            ctx.strokeStyle = 'black';
            ctx.lineWidth = 2;
            ctx.beginPath();
            ctx.arc(target.x, target.y + 5, 5, 0, Math.PI);
            ctx.stroke();
        }
    });

    // Draw bird
    if (bird) {
        bird.draw();
    }

    // Draw particles
    particles.forEach(p => {
        const alpha = p.life / p.maxLife;
        ctx.globalAlpha = alpha;
        ctx.fillStyle = p.color;
        ctx.beginPath();
        ctx.arc(p.x, p.y, 3, 0, Math.PI * 2);
        ctx.fill();
    });
    ctx.globalAlpha = 1;

    // Draw power indicator
    if (bird && bird.launched && !gameState.powerUsed) {
        ctx.fillStyle = 'rgba(255, 215, 0, 0.9)';
        ctx.font = 'bold 16px Comic Sans MS';
        ctx.textAlign = 'center';
        ctx.fillText('Press SPACE for boost!', canvas.width / 2, 50);
    }

    // Draw pause overlay
    if (gameState.paused) {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.7)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = 'white';
        ctx.font = 'bold 48px Comic Sans MS';
        ctx.textAlign = 'center';
        ctx.fillText('PAUSED', canvas.width / 2, canvas.height / 2);

        ctx.font = 'bold 24px Comic Sans MS';
        ctx.fillText('Press ESC to resume', canvas.width / 2, canvas.height / 2 + 50);
    }

    // Draw game over overlay
    if (gameState.gameOver) {
        ctx.fillStyle = 'rgba(0, 0, 0, 0.8)';
        ctx.fillRect(0, 0, canvas.width, canvas.height);

        ctx.fillStyle = '#FF6B6B';
        ctx.font = 'bold 48px Comic Sans MS';
        ctx.textAlign = 'center';
        ctx.fillText('GAME OVER', canvas.width / 2, canvas.height / 2 - 50);

        ctx.fillStyle = 'white';
        ctx.font = 'bold 24px Comic Sans MS';
        ctx.fillText(`Final Score: ${gameState.score}`, canvas.width / 2, canvas.height / 2);
        ctx.fillText('Press R to restart', canvas.width / 2, canvas.height / 2 + 50);
    }

    // Update UI
    document.getElementById('score').textContent = gameState.score;
    document.getElementById('level').textContent = gameState.level;
    document.getElementById('birds').textContent = gameState.birds;
}

// Game loop
function gameLoop() {
    if (!gameState.paused) {
        if (bird) {
            bird.update();

            // Check if bird is out of bounds or stopped
            if (bird.launched && (bird.x > canvas.width + 100 || bird.y > canvas.height + 100 ||
                (Math.abs(bird.vx) < 0.1 && Math.abs(bird.vy) < 0.1 && bird.y > canvas.height - 100))) {
                bird = null;
                gameState.birds--;
                gameState.powerUsed = false;
            }
        }

        updateParticles();

        // Check win condition
        if (targets.every(target => target.destroyed)) {
            gameState.level++;
            gameState.birds = 3;
            gameState.powerUsed = false;
            createTargets();

            // Level complete effect
            for (let i = 0; i < 30; i++) {
                particles.push({
                    x: canvas.width / 2 + (Math.random() - 0.5) * 200,
                    y: canvas.height / 2 + (Math.random() - 0.5) * 200,
                    vx: (Math.random() - 0.5) * 8,
                    vy: (Math.random() - 0.5) * 8,
                    life: 60,
                    maxLife: 60,
                    color: `hsl(${Math.random() * 360}, 70%, 60%)`
                });
            }
        }

        // Check game over
        if (gameState.birds <= 0 && !bird && !targets.every(target => target.destroyed)) {
            gameState.gameOver = true;
        }
    }

    draw();
    requestAnimationFrame(gameLoop);
}

// Input handling
let input = {
    mouse: { x: 0, y: 0, isDown: false, button: -1 },
    keys: {},
    touch: { x: 0, y: 0, isActive: false }
};

// Utility function to get mouse/touch position
function getInputPosition(e) {
    const rect = canvas.getBoundingClientRect();
    const scaleX = canvas.width / rect.width;
    const scaleY = canvas.height / rect.height;

    if (e.touches && e.touches.length > 0) {
        return {
            x: (e.touches[0].clientX - rect.left) * scaleX,
            y: (e.touches[0].clientY - rect.top) * scaleY
        };
    } else {
        return {
            x: (e.clientX - rect.left) * scaleX,
            y: (e.clientY - rect.top) * scaleY
        };
    }
}

// Mouse events
canvas.addEventListener('mousedown', (e) => {
    e.preventDefault();
    const pos = getInputPosition(e);
    input.mouse.x = pos.x;
    input.mouse.y = pos.y;
    input.mouse.isDown = true;
    input.mouse.button = e.button;

    handleInputStart(pos.x, pos.y);
});

canvas.addEventListener('mousemove', (e) => {
    e.preventDefault();
    const pos = getInputPosition(e);
    input.mouse.x = pos.x;
    input.mouse.y = pos.y;

    if (input.mouse.isDown) {
        handleInputMove(pos.x, pos.y);
    }
});

canvas.addEventListener('mouseup', (e) => {
    e.preventDefault();
    input.mouse.isDown = false;
    handleInputEnd();
});

canvas.addEventListener('mouseleave', (e) => {
    input.mouse.isDown = false;
    handleInputEnd();
});

// Touch events for mobile
canvas.addEventListener('touchstart', (e) => {
    e.preventDefault();
    const pos = getInputPosition(e);
    input.touch.x = pos.x;
    input.touch.y = pos.y;
    input.touch.isActive = true;

    handleInputStart(pos.x, pos.y);
});

canvas.addEventListener('touchmove', (e) => {
    e.preventDefault();
    const pos = getInputPosition(e);
    input.touch.x = pos.x;
    input.touch.y = pos.y;

    if (input.touch.isActive) {
        handleInputMove(pos.x, pos.y);
    }
});

canvas.addEventListener('touchend', (e) => {
    e.preventDefault();
    input.touch.isActive = false;
    handleInputEnd();
});

canvas.addEventListener('touchcancel', (e) => {
    e.preventDefault();
    input.touch.isActive = false;
    handleInputEnd();
});

// Keyboard events
document.addEventListener('keydown', (e) => {
    input.keys[e.code] = true;

    // Handle keyboard shortcuts
    switch(e.code) {
        case 'Space':
            e.preventDefault();
            if (bird && bird.launched && !gameState.powerUsed) {
                // Special bird ability - boost
                bird.vy -= 5;
                bird.vx *= 1.2;
                gameState.powerUsed = true;
                createExplosion(bird.x, bird.y, '#FFD700');
                console.log('💥 Power boost activated!');
            }
            break;
        case 'KeyR':
            e.preventDefault();
            restartGame();
            break;
        case 'Escape':
            e.preventDefault();
            // Pause/unpause game
            gameState.paused = !gameState.paused;
            break;
    }
});

document.addEventListener('keyup', (e) => {
    input.keys[e.code] = false;
});

// Unified input handling functions
function handleInputStart(x, y) {
    if (!bird && gameState.birds > 0 && !gameState.paused) {
        // Check if clicking near slingshot
        const dx = x - slingshot.x;
        const dy = y - slingshot.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance <= slingshot.radius + 20) {
            bird = new Bird(slingshot.x, slingshot.y);
            gameState.isAiming = true;
            gameState.isDragging = true;
            canvas.style.cursor = 'grabbing';
        }
    }
}

function handleInputMove(x, y) {
    if (gameState.isDragging && bird && !bird.launched && !gameState.paused) {
        const dx = x - slingshot.x;
        const dy = y - slingshot.y;
        const distance = Math.sqrt(dx * dx + dy * dy);
        const maxDistance = 120;

        // Constrain bird position within slingshot range
        if (distance > maxDistance) {
            bird.x = slingshot.x + (dx / distance) * maxDistance;
            bird.y = slingshot.y + (dy / distance) * maxDistance;
        } else {
            bird.x = x;
            bird.y = y;
        }

        // Calculate trajectory with better physics
        calculateTrajectory();

        // Visual feedback - change cursor
        canvas.style.cursor = 'grabbing';
    } else if (!gameState.isDragging) {
        // Check if hovering over interactive elements
        const dx = x - slingshot.x;
        const dy = y - slingshot.y;
        const distance = Math.sqrt(dx * dx + dy * dy);

        if (distance <= slingshot.radius + 20 && !bird && gameState.birds > 0) {
            canvas.style.cursor = 'grab';
        } else {
            canvas.style.cursor = 'crosshair';
        }
    }
}

function handleInputEnd() {
    if (gameState.isDragging && bird && !bird.launched && !gameState.paused) {
        const dx = slingshot.x - bird.x;
        const dy = slingshot.y - bird.y;
        const power = Math.sqrt(dx * dx + dy * dy);

        // Minimum power threshold
        if (power > 10) {
            const powerMultiplier = Math.min(power / 8, 20);
            bird.vx = (dx / power) * powerMultiplier;
            bird.vy = (dy / power) * powerMultiplier;
            bird.launched = true;

            // Add launch sound effect placeholder
            console.log('🚀 Bird launched with power:', powerMultiplier.toFixed(1));
        } else {
            // Not enough power, reset bird
            bird = null;
        }

        gameState.isAiming = false;
        gameState.isDragging = false;
        canvas.style.cursor = 'crosshair';
    }
}

// Improved trajectory calculation
function calculateTrajectory() {
    trajectory = [];
    const dx = slingshot.x - bird.x;
    const dy = slingshot.y - bird.y;
    const power = Math.sqrt(dx * dx + dy * dy);

    if (power > 0) {
        const powerMultiplier = Math.min(power / 8, 20);
        let tx = bird.x;
        let ty = bird.y;
        let tvx = (dx / power) * powerMultiplier;
        let tvy = (dy / power) * powerMultiplier;

        for (let i = 0; i < 50; i++) {
            trajectory.push({ x: tx, y: ty });
            tvx *= FRICTION;
            tvy *= FRICTION;
            tvy += GRAVITY;
            tx += tvx;
            ty += tvy;

            // Stop trajectory at ground or screen bounds
            if (ty > canvas.height - 50 || tx > canvas.width + 50) break;
        }
    }
}

// Restart game
function restartGame() {
    gameState = {
        score: 0,
        level: 1,
        birds: 3,
        isAiming: false,
        isDragging: false,
        gameOver: false,
        paused: false,
        powerUsed: false
    };
    bird = null;
    trajectory = [];
    particles = [];
    createTargets();
    canvas.style.cursor = 'crosshair';
    console.log('🔄 Game restarted!');
}

// Start the game
init();
