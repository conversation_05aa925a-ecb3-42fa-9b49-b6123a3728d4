// Advanced Audio System for Bird Blast Game
class AudioSystem {
    constructor() {
        this.audioContext = null;
        this.masterGain = null;
        this.sounds = new Map();
        this.music = new Map();
        this.isInitialized = false;
        this.isMuted = false;
        this.masterVolume = 0.7;
        this.sfxVolume = 0.8;
        this.musicVolume = 0.5;
        
        // Audio pools for performance
        this.audioPool = new Map();
        this.maxPoolSize = 10;
    }

    async initialize() {
        try {
            // Create audio context
            this.audioContext = new (window.AudioContext || window.webkitAudioContext)();
            
            // Create master gain node
            this.masterGain = this.audioContext.createGain();
            this.masterGain.connect(this.audioContext.destination);
            this.masterGain.gain.value = this.masterVolume;
            
            // Generate procedural sounds
            this.generateProceduralSounds();
            
            this.isInitialized = true;
            console.log('🔊 Audio system initialized');
            
        } catch (error) {
            console.warn('Audio initialization failed:', error);
        }
    }

    // Generate sounds procedurally using Web Audio API
    generateProceduralSounds() {
        // <PERSON> launch sound
        this.createSound('birdLaunch', () => {
            return this.createSweepSound(200, 800, 0.3, 'sawtooth');
        });

        // Explosion sound
        this.createSound('explosion', () => {
            return this.createNoiseSound(0.5, 'lowpass', 2000);
        });

        // Target hit sound
        this.createSound('targetHit', () => {
            return this.createToneSound(440, 0.2, 'square');
        });

        // Power-up sound
        this.createSound('powerUp', () => {
            return this.createSweepSound(440, 880, 0.4, 'sine');
        });

        // Level complete sound
        this.createSound('levelComplete', () => {
            return this.createMelodySound([523, 659, 784, 1047], 0.15);
        });

        // UI click sound
        this.createSound('uiClick', () => {
            return this.createToneSound(800, 0.1, 'sine');
        });

        // Bounce sound
        this.createSound('bounce', () => {
            return this.createToneSound(300, 0.15, 'triangle');
        });

        // Whoosh sound
        this.createSound('whoosh', () => {
            return this.createSweepSound(100, 50, 0.3, 'sawtooth');
        });
    }

    createSound(name, generator) {
        this.sounds.set(name, generator);
        this.audioPool.set(name, []);
    }

    // Create tone sound
    createToneSound(frequency, duration, waveType = 'sine') {
        return () => {
            if (!this.audioContext) return;

            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.type = waveType;
            oscillator.frequency.setValueAtTime(frequency, this.audioContext.currentTime);
            
            // Envelope
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.3, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
            
            oscillator.connect(gainNode);
            gainNode.connect(this.masterGain);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
            
            return { oscillator, gainNode };
        };
    }

    // Create frequency sweep sound
    createSweepSound(startFreq, endFreq, duration, waveType = 'sine') {
        return () => {
            if (!this.audioContext) return;

            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.type = waveType;
            oscillator.frequency.setValueAtTime(startFreq, this.audioContext.currentTime);
            oscillator.frequency.exponentialRampToValueAtTime(endFreq, this.audioContext.currentTime + duration);
            
            // Envelope
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.4, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
            
            oscillator.connect(gainNode);
            gainNode.connect(this.masterGain);
            
            oscillator.start(this.audioContext.currentTime);
            oscillator.stop(this.audioContext.currentTime + duration);
            
            return { oscillator, gainNode };
        };
    }

    // Create noise sound (for explosions)
    createNoiseSound(duration, filterType = 'lowpass', filterFreq = 1000) {
        return () => {
            if (!this.audioContext) return;

            const bufferSize = this.audioContext.sampleRate * duration;
            const buffer = this.audioContext.createBuffer(1, bufferSize, this.audioContext.sampleRate);
            const data = buffer.getChannelData(0);
            
            // Generate white noise
            for (let i = 0; i < bufferSize; i++) {
                data[i] = Math.random() * 2 - 1;
            }
            
            const source = this.audioContext.createBufferSource();
            const filter = this.audioContext.createBiquadFilter();
            const gainNode = this.audioContext.createGain();
            
            source.buffer = buffer;
            filter.type = filterType;
            filter.frequency.setValueAtTime(filterFreq, this.audioContext.currentTime);
            
            // Envelope for explosion
            gainNode.gain.setValueAtTime(0, this.audioContext.currentTime);
            gainNode.gain.linearRampToValueAtTime(0.5, this.audioContext.currentTime + 0.01);
            gainNode.gain.exponentialRampToValueAtTime(0.001, this.audioContext.currentTime + duration);
            
            source.connect(filter);
            filter.connect(gainNode);
            gainNode.connect(this.masterGain);
            
            source.start(this.audioContext.currentTime);
            
            return { source, filter, gainNode };
        };
    }

    // Create melody sound
    createMelodySound(frequencies, noteDuration) {
        return () => {
            if (!this.audioContext) return;

            frequencies.forEach((freq, index) => {
                const startTime = this.audioContext.currentTime + index * noteDuration;
                const oscillator = this.audioContext.createOscillator();
                const gainNode = this.audioContext.createGain();
                
                oscillator.type = 'sine';
                oscillator.frequency.setValueAtTime(freq, startTime);
                
                gainNode.gain.setValueAtTime(0, startTime);
                gainNode.gain.linearRampToValueAtTime(0.3, startTime + 0.01);
                gainNode.gain.exponentialRampToValueAtTime(0.001, startTime + noteDuration);
                
                oscillator.connect(gainNode);
                gainNode.connect(this.masterGain);
                
                oscillator.start(startTime);
                oscillator.stop(startTime + noteDuration);
            });
        };
    }

    // Play sound effect
    playSound(name, options = {}) {
        if (!this.isInitialized || this.isMuted || !this.sounds.has(name)) return;

        try {
            // Resume audio context if suspended (required by some browsers)
            if (this.audioContext.state === 'suspended') {
                this.audioContext.resume();
            }

            const soundGenerator = this.sounds.get(name);
            const audioNodes = soundGenerator();
            
            // Apply volume
            if (audioNodes && audioNodes.gainNode) {
                const volume = (options.volume || 1) * this.sfxVolume;
                audioNodes.gainNode.gain.value *= volume;
            }

            // Apply pitch variation
            if (options.pitch && audioNodes && audioNodes.oscillator) {
                const pitchMultiplier = Math.pow(2, options.pitch / 12); // Semitone conversion
                audioNodes.oscillator.frequency.value *= pitchMultiplier;
            }

            console.log(`🔊 Playing sound: ${name}`);
            
        } catch (error) {
            console.warn(`Failed to play sound ${name}:`, error);
        }
    }

    // Play sound with spatial audio (3D positioning)
    playSpatialSound(name, x, y, listenerX, listenerY, options = {}) {
        if (!this.isInitialized || this.isMuted) return;

        const distance = Math.sqrt((x - listenerX) ** 2 + (y - listenerY) ** 2);
        const maxDistance = options.maxDistance || 500;
        const volume = Math.max(0, 1 - distance / maxDistance);
        
        if (volume > 0) {
            this.playSound(name, { ...options, volume: volume * (options.volume || 1) });
        }
    }

    // Background music system
    createBackgroundMusic() {
        // Simple procedural background music
        if (!this.audioContext) return;

        const playNote = (frequency, startTime, duration) => {
            const oscillator = this.audioContext.createOscillator();
            const gainNode = this.audioContext.createGain();
            
            oscillator.type = 'sine';
            oscillator.frequency.setValueAtTime(frequency, startTime);
            
            gainNode.gain.setValueAtTime(0, startTime);
            gainNode.gain.linearRampToValueAtTime(0.1 * this.musicVolume, startTime + 0.1);
            gainNode.gain.linearRampToValueAtTime(0, startTime + duration);
            
            oscillator.connect(gainNode);
            gainNode.connect(this.masterGain);
            
            oscillator.start(startTime);
            oscillator.stop(startTime + duration);
        };

        // Simple chord progression
        const chords = [
            [261.63, 329.63, 392.00], // C major
            [293.66, 369.99, 440.00], // D minor
            [329.63, 415.30, 493.88], // E minor
            [349.23, 440.00, 523.25]  // F major
        ];

        let currentTime = this.audioContext.currentTime;
        const chordDuration = 2;

        chords.forEach((chord, index) => {
            chord.forEach(frequency => {
                playNote(frequency, currentTime + index * chordDuration, chordDuration);
            });
        });
    }

    // Volume controls
    setMasterVolume(volume) {
        this.masterVolume = Math.max(0, Math.min(1, volume));
        if (this.masterGain) {
            this.masterGain.gain.value = this.masterVolume;
        }
    }

    setSFXVolume(volume) {
        this.sfxVolume = Math.max(0, Math.min(1, volume));
    }

    setMusicVolume(volume) {
        this.musicVolume = Math.max(0, Math.min(1, volume));
    }

    // Mute/unmute
    toggleMute() {
        this.isMuted = !this.isMuted;
        if (this.masterGain) {
            this.masterGain.gain.value = this.isMuted ? 0 : this.masterVolume;
        }
        return this.isMuted;
    }

    // Initialize on user interaction (required by browsers)
    async initializeOnUserInteraction() {
        if (!this.isInitialized) {
            await this.initialize();
        }
        
        if (this.audioContext && this.audioContext.state === 'suspended') {
            await this.audioContext.resume();
        }
    }

    // Cleanup
    destroy() {
        if (this.audioContext) {
            this.audioContext.close();
        }
        this.sounds.clear();
        this.audioPool.clear();
    }
}

// Audio manager singleton
const audioManager = new AudioSystem();

// Auto-initialize on first user interaction
let audioInitialized = false;
const initAudioOnInteraction = async () => {
    if (!audioInitialized) {
        await audioManager.initializeOnUserInteraction();
        audioInitialized = true;
        
        // Remove event listeners after initialization
        document.removeEventListener('click', initAudioOnInteraction);
        document.removeEventListener('keydown', initAudioOnInteraction);
        document.removeEventListener('touchstart', initAudioOnInteraction);
    }
};

document.addEventListener('click', initAudioOnInteraction);
document.addEventListener('keydown', initAudioOnInteraction);
document.addEventListener('touchstart', initAudioOnInteraction);

// Export for use in main game
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { AudioSystem, audioManager };
}
