<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bird Blast - Fun Physics Game</title>
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;600;700;800&display=swap');

        :root {
            --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            --warning-gradient: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            --glass-bg: rgba(255, 255, 255, 0.15);
            --glass-border: rgba(255, 255, 255, 0.2);
            --text-primary: #2d3748;
            --text-secondary: #4a5568;
            --shadow-soft: 0 8px 32px rgba(31, 38, 135, 0.37);
            --shadow-strong: 0 15px 35px rgba(31, 38, 135, 0.5);
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Poppins', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            background-size: 400% 400%;
            animation: gradientShift 15s ease infinite;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            overflow-x: hidden;
            position: relative;
        }

        @keyframes gradientShift {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        /* Floating background elements */
        body::before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background:
                radial-gradient(circle at 20% 80%, rgba(120, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 20%, rgba(255, 119, 198, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 40% 40%, rgba(120, 219, 255, 0.3) 0%, transparent 50%);
            pointer-events: none;
            z-index: -1;
        }

        .header {
            text-align: center;
            padding: 30px 20px;
            backdrop-filter: blur(20px);
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: 25px;
            margin: 20px;
            box-shadow: var(--shadow-soft);
            animation: fadeInDown 1s ease-out;
        }

        @keyframes fadeInDown {
            from {
                opacity: 0;
                transform: translateY(-30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .header h1 {
            font-size: 3.5em;
            font-weight: 800;
            margin-bottom: 15px;
            background: linear-gradient(45deg, #ff6b6b, #4ecdc4, #45b7d1, #96ceb4, #feca57);
            background-size: 300% 300%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            animation: textGradient 3s ease infinite;
            text-shadow: 0 0 30px rgba(255, 255, 255, 0.5);
        }

        @keyframes textGradient {
            0%, 100% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
        }

        .header p {
            font-size: 1.2em;
            color: white;
            font-weight: 400;
            text-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
            opacity: 0.9;
        }

        .game-container {
            position: relative;
            backdrop-filter: blur(20px);
            background: var(--glass-bg);
            border: 2px solid var(--glass-border);
            border-radius: 30px;
            box-shadow: var(--shadow-strong);
            overflow: hidden;
            animation: fadeInUp 1s ease-out 0.3s both;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }

        .game-container:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(31, 38, 135, 0.6);
        }

        @keyframes fadeInUp {
            from {
                opacity: 0;
                transform: translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        #gameCanvas {
            display: block;
            cursor: crosshair;
            touch-action: none;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        .ui-panel {
            position: absolute;
            top: 15px;
            left: 15px;
            backdrop-filter: blur(15px);
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            padding: 20px;
            border-radius: 20px;
            box-shadow: var(--shadow-soft);
            font-weight: 600;
            color: white;
            min-width: 150px;
            animation: slideInLeft 0.8s ease-out 0.5s both;
        }

        @keyframes slideInLeft {
            from {
                opacity: 0;
                transform: translateX(-30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .score {
            font-size: 1.6em;
            margin-bottom: 8px;
            background: var(--success-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 700;
        }

        .level {
            font-size: 1.3em;
            margin-bottom: 8px;
            background: var(--warning-gradient);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            font-weight: 600;
        }

        .birds {
            font-size: 1.2em;
            color: #ff6b6b;
            font-weight: 600;
        }

        .controls {
            position: absolute;
            bottom: 15px;
            left: 50%;
            transform: translateX(-50%);
            backdrop-filter: blur(15px);
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            padding: 15px 25px;
            border-radius: 30px;
            box-shadow: var(--shadow-soft);
            color: white;
            font-weight: 500;
            text-align: center;
            animation: slideInUp 0.8s ease-out 0.7s both;
            font-size: 0.9em;
        }

        @keyframes slideInUp {
            from {
                opacity: 0;
                transform: translateX(-50%) translateY(30px);
            }
            to {
                opacity: 1;
                transform: translateX(-50%) translateY(0);
            }
        }

        .restart-btn {
            position: absolute;
            top: 15px;
            right: 15px;
            background: var(--secondary-gradient);
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 25px;
            font-size: 1em;
            font-weight: 600;
            cursor: pointer;
            box-shadow: var(--shadow-soft);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideInRight 0.8s ease-out 0.6s both;
        }

        @keyframes slideInRight {
            from {
                opacity: 0;
                transform: translateX(30px);
            }
            to {
                opacity: 1;
                transform: translateX(0);
            }
        }

        .restart-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 25px rgba(245, 87, 108, 0.4);
        }

        .restart-btn:active {
            transform: translateY(0);
        }

        /* Audio control button */
        .audio-btn {
            position: absolute;
            top: 70px;
            right: 15px;
            background: var(--primary-gradient);
            color: white;
            border: none;
            padding: 10px;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 1.2em;
            cursor: pointer;
            box-shadow: var(--shadow-soft);
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            animation: slideInRight 0.8s ease-out 0.8s both;
        }

        .audio-btn:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }
    </style>
</head>
<body>
    <div class="header">
        <h1>🐦 Bird Blast 🎯</h1>
        <p>Aim, shoot, and destroy all targets!</p>
    </div>

    <div class="game-container">
        <canvas id="gameCanvas" width="800" height="600"></canvas>
        
        <div class="ui-panel">
            <div class="score">Score: <span id="score">0</span></div>
            <div class="level">Level: <span id="level">1</span></div>
            <div class="birds">Birds: <span id="birds">3</span></div>
        </div>

        <div class="controls">
            🖱️ Click & drag to aim • Release to shoot<br>
            ⌨️ SPACE: Power boost • R: Restart • ESC: Pause
        </div>

        <button class="restart-btn" onclick="restartGame()">🔄 Restart</button>
        <button class="audio-btn" onclick="toggleAudio()" id="audioBtn">🔊</button>
    </div>

    <script src="physics.js"></script>
    <script src="particles.js"></script>
    <script src="audio.js"></script>
    <script src="game.js"></script>
</body>
</html>
